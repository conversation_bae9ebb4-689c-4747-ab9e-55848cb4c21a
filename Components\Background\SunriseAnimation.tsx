import React, { useLayoutEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { gsap } from 'gsap';

// Interface pour les méthodes exposées du composant
export interface SunriseAnimationRef {
  triggerSunrise: () => void;
  resetSun: () => void;
}

// Interface pour les props du composant
interface SunriseAnimationProps {
  isVisible: boolean; // Contrôle la visibilité du composant
}

const SunriseAnimation = forwardRef<SunriseAnimationRef, SunriseAnimationProps>(
  ({ isVisible }, ref) => {
    // Références pour les éléments DOM - VERSION SIMPLIFIÉE
    const containerRef = useRef<HTMLDivElement>(null);
    const sunWrapperRef = useRef<HTMLDivElement>(null);
    const sunGlowRef = useRef<HTMLDivElement>(null);
    const lensFlareRef = useRef<HTMLDivElement>(null);
    const sunImageRef = useRef<HTMLImageElement>(null);

    // Référence pour la timeline GSAP
    const timelineRef = useRef<gsap.core.Timeline | null>(null);

    // 🌅 FONCTION PRINCIPALE: Déclencher l'animation de lever de soleil - VERSION SIMPLIFIÉE
    const triggerSunrise = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌅 Éléments DOM non prêts pour l\'animation');
        return;
      }

      console.log('🌅 Déclenchement de l\'animation de lever de soleil - Version réaliste');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation de lever de soleil terminée');
        }
      });

      // Position initiale - soleil simple
      gsap.set(sunWrapperRef.current, {
        y: '60%',
        opacity: 1
      });
      gsap.set(sunGlowRef.current, {
        opacity: 0,
        scale: 0.8
      });
      gsap.set(lensFlareRef.current, {
        opacity: 0
        // CISCO: Pas de scale pour éviter la déformation
      });

      // PHASE 1: Le soleil monte TRÈS haut sur l'horizon - CISCO
      timelineRef.current.fromTo(
        sunWrapperRef.current,
        { y: '60%' },
        {
          y: '5%', // CISCO: TRÈS haut pour visibilité maximale (10% → 5%)
          duration: 12.0,
          ease: 'power1.out'
        },
        0
      );

      // PHASE 2: La lueur EXTRA LUMINEUX apparaît
      timelineRef.current.fromTo(
        sunGlowRef.current,
        { opacity: 0, scale: 0.8 },
        {
          opacity: 1.0, // CISCO: Opacité maximale pour visibilité
          scale: 1.2,   // CISCO: Plus grand pour plus d'impact
          duration: 8.0,
          ease: 'power2.out'
        },
        2
      );

      // PHASE 3: Le lens flare PNG - CISCO: Pas de déformation, juste déplacement
      timelineRef.current.fromTo(
        lensFlareRef.current,
        {
          opacity: 0,
          x: '-30%',  // CISCO: Commence en haut à gauche
          y: '-20%'   // CISCO: Position haute
        },
        {
          opacity: 1.0,
          x: '30%',   // CISCO: Finit en bas à droite
          y: '20%',   // CISCO: Position basse
          duration: 8.0,
          ease: 'power2.inOut' // CISCO: Courbe fluide pour mouvement naturel
        },
        4
      );
    };

    // 🔄 FONCTION: Remettre le soleil en position initiale - VERSION SIMPLIFIÉE
    const resetSun = () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      if (sunWrapperRef.current && sunGlowRef.current && lensFlareRef.current) {
        gsap.set(sunWrapperRef.current, {
          y: '60%',
          opacity: 1
        });
        gsap.set(sunGlowRef.current, {
          opacity: 0,
          scale: 0.8
        });
        gsap.set(lensFlareRef.current, {
          opacity: 0
          // CISCO: Pas de scale pour éviter la déformation
        });
      }

      console.log('🔄 Soleil remis en position initiale');
    };

    // Exposer les méthodes via useImperativeHandle
    useImperativeHandle(ref, () => ({
      triggerSunrise,
      resetSun
    }));

    // Cleanup à la destruction du composant
    useLayoutEffect(() => {
      return () => {
        if (timelineRef.current) {
          timelineRef.current.kill();
        }
      };
    }, []);

    // Ne pas rendre si non visible
    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={containerRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 1.8 }} // 🔧 CISCO: Soleil reste derrière les collines (z-index 5) - NE PAS TOUCHER
      >
        {/* Conteneur pour le soleil et ses effets - CISCO: Soleil légèrement plus grand */}
        <div
          ref={sunWrapperRef}
          className="absolute w-40 h-40 left-1/2 top-1/2 -translate-x-1/2"
          style={{
            transform: 'translateX(-50%) translateY(60%)', // Position initiale plus haute (100px de moins)
          }}
        >
          <div className="relative w-full h-full">
            {/* EFFET 1: Lueur subtile du soleil */}
            <div
              ref={sunGlowRef}
              className="sun-glow absolute inset-0 opacity-0"
            />

            {/* EFFET 2: Lens Flare PNG - RETIRÉ DU SOLEIL */}
            {/* Le lens-flare sera maintenant dans un conteneur séparé devant les collines */}

            {/* L'image du soleil - CISCO: Plus lumineux, moins "balle de tennis" */}
            <img
              ref={sunImageRef}
              src="/SUN.png"
              alt="Soleil"
              className="relative z-10 w-full h-full object-contain"
              style={{
                filter: 'brightness(1.8) contrast(1.3) saturate(1.4) drop-shadow(0 0 25px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 15px rgba(255, 220, 0, 0.6))'
              }}
            />
          </div>
        </div>

        {/* CONTENEUR SÉPARÉ POUR LE LENS-FLARE - CISCO: Devant le background ET les collines */}
        <div
          className="fixed inset-0 w-full h-full pointer-events-none"
          style={{ zIndex: 8 }} // CISCO: Devant le background (z-index 5) ET les collines pour visibilité
        >
          <div
            className="absolute w-40 h-40 left-1/2 top-1/2 -translate-x-1/2"
            style={{
              transform: 'translateX(-50%) translateY(60%)', // Même position que le soleil initialement
            }}
          >
            {/* EFFET: Lens Flare CSS Optique - CISCO: Hexagone et cercles colorés */}
            <div
              ref={lensFlareRef}
              className="lens-flare-optical absolute opacity-0"
              style={{
                width: '200px',
                height: '200px',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)'
              }}
            />
          </div>
        </div>
      </div>
    );
  }
);

SunriseAnimation.displayName = 'SunriseAnimation';

export default SunriseAnimation;
